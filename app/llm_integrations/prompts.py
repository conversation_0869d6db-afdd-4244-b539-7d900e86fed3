from langchain_core.prompts import PromptTemplate

# 示例 PromptTemplate
example_prompt = PromptTemplate(
    input_variables=["input"],
    template="请处理以下文本： {input}"
)

# 你可以在这里定义更多的 PromptTemplate
# text_classification_prompt = PromptTemplate(...)
# summarization_prompt = PromptTemplate(...)

# 事件提取
extract_json_prompt = PromptTemplate(
    input_variables=["input"],
    template='''
    **任务：** 从以下提供的文本中**详尽地提取所有独立的关键事件**。确保每一个被识别的事件都有其独立的触发词和相关的事件论元。
    
    **提取要求：**
    对于文本中识别出的每一个事件，请提取以下信息：
    1.  **事件描述 (Event Description):** 对事件的简要概括。
    2.  **事件类型 (Event Type):** 对事件简要概括的类型。
    3.  **事件触发词 (Event Trigger):** 指示事件发生的关键词或短语。
    4.  **事件论元 (Event Arguments):** 构成该具体事件核心或描述其背景的要素。
        * 事件论元列表中的每个对象应代表一个特定的 **角色 (role)** 及其对应的所有 **实体/值 (values)**。
        * 每个这样的对象应包含：
            * `"role"`: 字符串，表示该论元在事件中扮演的角色。
            * `"values"`: 列表，包含所有从文本中提取到的、扮演该角色的具体实体或值。**即使只有一个实体/值，也请使用列表形式。**
        * **论元角色应尽可能精确地描述该成分在该具体事件中的功能，应为动词或形容词，不可出现专有名词和人员姓名**。例如：施事者 (Agent)、受事者 (Patient/Theme)、工具 (Instrument)、时间 (Time)、地点 (Location)、原因 (Reason)、目的 (Purpose)、方式 (Manner)、金额 (Amount)、参与者 (Participant)、合作伙伴 (Partner)、开始时间 (Start Time)、结束时间 (End Time)、报告日期 (Report Date) 等。
        * **如果一个事件本身包含多个不同的时间信息（例如，事件宣布时间、事件执行时间），请将它们作为具有不同角色的独立论元条目提取出来。** 如果多个实体/值对应同一个角色（例如，多个“参与者”），则应将它们收集到相应角色下的 "values" 列表中。
        * **论元中不需要包含上述已存在的信息，比如事件类型、时间触发词等。** 
    5.  **其他关键信息 (Other Key Information):** 任何其他与该具体事件相关但未被明确归类为上述论元的重要细节。
    
    **输出格式：**
    请以JSON格式返回结果。结果应该是一个列表，其中**列表中的每个对象代表一个从文本中识别出的独立事件**，并包含该事件的上述提取字段。**事件论元 (Event Arguments) 本身是一个列表，其中每个元素是一个对象，该对象包含 "role" 字段和 "values" 列表字段。** 如果文本中没有识别到明确的事件，请返回一个空列表 `[]`。
    **注意：最终输出必须是一个能够被标准JSON解析器直接解析的、完全合法的JSON数据。**
    
    **示例：**
    
    如果文本是："昨日下午，创新科技在深圳总部召开发布会，正式发布了其最新款智能手机“Vision Pro X”，该产品计划于下周一开始在线上销售。另外，该公司CEO张三还透露，他们将在今年第四季度启动一项代号为“未来”的人工智能研究项目。"
    期望的输出可能是：
    ```json
    [
      {
        "Event Description": "创新科技发布新款智能手机Vision Pro X并公布销售计划",
        "Event Type": "产品行为-发布",
        "Event Trigger": "发布",
        "Event Arguments": [
          {"role": "发布方", "values": ["创新科技"]},
          {"role": "产品名称)", "values": ["Vision Pro X"]},
          {"role": "产品类型", "values": ["智能手机"]},
          {"role": "发布时间", "values": ["昨日下午"]},
          {"role": "发布地点)", "values": ["深圳总部"]},
          {"role": "计划销售开始时间", "values": ["下周一开始"]}
        ],
        "Other Key Information": "发布会形式为召开发布会。"
      },
      {
        "Event Description": "创新科技高管宣布公司将启动AI研究项目“未来”并确认了首批合作伙伴",
        "Event Type": "组织关系-加盟",
        "Event Trigger": "宣布",
        "Event Arguments": [
          {"role": "宣布方", "values": ["该公司CEO张三", "CTO李四"]},
          {"role": "项目发起公司", "values": ["他们公司"]},
          {"role": "计划启动时间", "values": ["今年第四季度"]},
          {"role": "合作伙伴", "values": ["大学A", "B公司", "研究员王五"]}
        ],
        "Other Key Information": null
      }
    ]
    ```
    
    **文本：**{input}
    '''
)
