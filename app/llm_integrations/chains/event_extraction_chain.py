"""
事件提取LangChain Chain实现

该模块提供了用于从文本中提取事件信息的LangChain chain，
集成了LLM模型、提示模板和JSON解析功能。
"""

import json
from typing import List, Dict, Any, Optional
from langchain_core.output_parsers import BaseOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.runnables.base import Runnable

from app.llm_integrations.llm_models import LlmModel
from app.llm_integrations.prompts import extract_json_prompt
from app.utils.json_extractor import JsonExtractor
from app.api.v1.schemas.event_extraction_schemas import EventExtractionResponse, EventExtractionArgument
from app.core.logger_config import get_logger

logger = get_logger()


class EventExtractionOutputParser(BaseOutputParser[List[EventExtractionResponse]]):
    """
    事件提取输出解析器
    
    将LLM的JSON输出解析为EventExtractionResponse对象列表
    """
    
    def __init__(self):
        super().__init__()
        self.json_extractor = JsonExtractor()
    
    def parse(self, text: str) -> List[EventExtractionResponse]:
        """
        解析LLM输出文本，提取事件信息
        
        Args:
            text: LLM的原始输出文本
            
        Returns:
            List[EventExtractionResponse]: 解析后的事件列表
        """
        try:
            logger.debug(f"开始解析LLM输出: {text[:200]}...")
            
            # 使用JsonExtractor提取JSON
            json_strings = self.json_extractor.extract(text)
            
            if not json_strings:
                logger.warning("未从LLM输出中提取到有效的JSON")
                return []
            
            # 取第一个有效的JSON字符串
            json_str = json_strings[0]
            logger.debug(f"提取到的JSON字符串: {json_str}")
            
            # 解析JSON
            raw_events = json.loads(json_str)
            
            if not isinstance(raw_events, list):
                logger.warning(f"期望JSON为列表格式，但得到: {type(raw_events)}")
                return []
            
            # 转换为EventExtractionResponse对象
            events = []
            for raw_event in raw_events:
                try:
                    event = self._parse_single_event(raw_event)
                    if event:
                        events.append(event)
                except Exception as e:
                    logger.error(f"解析单个事件时出错: {e}, 原始数据: {raw_event}")
                    continue
            
            logger.info(f"成功解析出 {len(events)} 个事件")
            return events
            
        except Exception as e:
            logger.error(f"解析LLM输出时出错: {e}")
            return []
    
    def _parse_single_event(self, raw_event: Dict[str, Any]) -> Optional[EventExtractionResponse]:
        """
        解析单个事件数据
        
        Args:
            raw_event: 原始事件数据字典
            
        Returns:
            Optional[EventExtractionResponse]: 解析后的事件对象，失败时返回None
        """
        try:
            # 提取基本字段，处理可能的字段名变化
            event_type = (raw_event.get("Event Type") or 
                         raw_event.get("event_type") or 
                         raw_event.get("type", ""))
            
            event_trigger = (raw_event.get("Event Trigger") or 
                           raw_event.get("event_trigger") or 
                           raw_event.get("trigger", ""))
            
            event_description = (raw_event.get("Event Description") or 
                               raw_event.get("event_description") or 
                               raw_event.get("description", ""))
            
            # 解析事件论元
            raw_arguments = (raw_event.get("Event Arguments") or 
                           raw_event.get("event_arguments") or 
                           raw_event.get("arguments", []))
            
            arguments = []
            if isinstance(raw_arguments, list):
                for raw_arg in raw_arguments:
                    if isinstance(raw_arg, dict) and "role" in raw_arg and "values" in raw_arg:
                        arg = EventExtractionArgument(
                            role=raw_arg["role"],
                            values=raw_arg["values"] if isinstance(raw_arg["values"], list) else [raw_arg["values"]]
                        )
                        arguments.append(arg)
            
            # 创建事件对象
            event = EventExtractionResponse(
                type=event_type,
                trigger=event_trigger,
                description=event_description,
                arguments=arguments
            )
            
            return event
            
        except Exception as e:
            logger.error(f"解析单个事件数据时出错: {e}")
            return None
    
    @property
    def _type(self) -> str:
        return "event_extraction_output_parser"


class EventExtractionChain:
    """
    事件提取Chain类
    
    封装了完整的事件提取流程，包括LLM调用、输出解析等
    """
    
    def __init__(self):
        """初始化事件提取Chain"""
        self.llm_model = LlmModel()
        self.llm = self.llm_model.get_openai_compatible_llm()
        self.output_parser = EventExtractionOutputParser()
        self.chain = self._build_chain()
        
        logger.info("事件提取Chain初始化完成")
    
    def _build_chain(self) -> Runnable:
        """
        构建LangChain处理链
        
        Returns:
            Runnable: 构建好的处理链
        """
        # 构建处理链: 提示模板 -> LLM -> 输出解析器
        chain = (
            RunnablePassthrough.assign(
                formatted_prompt=lambda x: extract_json_prompt.format(input=x["input"])
            )
            | (lambda x: extract_json_prompt.format(input=x["input"]))
            | self.llm
            | self.output_parser
        )
        
        return chain
    
    def extract_events(self, text: str) -> List[EventExtractionResponse]:
        """
        从文本中提取事件信息
        
        Args:
            text: 待分析的文本
            
        Returns:
            List[EventExtractionResponse]: 提取到的事件列表
        """
        try:
            logger.info(f"开始提取事件，文本长度: {len(text)}")
            
            # 调用处理链
            result = self.chain.invoke({"input": text})
            
            logger.info(f"事件提取完成，共提取到 {len(result)} 个事件")
            return result
            
        except Exception as e:
            logger.error(f"事件提取过程中出错: {e}")
            return []
    
    async def aextract_events(self, text: str) -> List[EventExtractionResponse]:
        """
        异步版本的事件提取
        
        Args:
            text: 待分析的文本
            
        Returns:
            List[EventExtractionResponse]: 提取到的事件列表
        """
        try:
            logger.info(f"开始异步提取事件，文本长度: {len(text)}")
            
            # 调用异步处理链
            result = await self.chain.ainvoke({"input": text})
            
            logger.info(f"异步事件提取完成，共提取到 {len(result)} 个事件")
            return result
            
        except Exception as e:
            logger.error(f"异步事件提取过程中出错: {e}")
            return []


# 全局实例，避免重复初始化
_event_extraction_chain_instance: Optional[EventExtractionChain] = None


def get_event_extraction_chain() -> EventExtractionChain:
    """
    获取事件提取Chain实例（单例模式）
    
    Returns:
        EventExtractionChain: 事件提取Chain实例
    """
    global _event_extraction_chain_instance
    
    if _event_extraction_chain_instance is None:
        logger.info("创建新的事件提取Chain实例")
        _event_extraction_chain_instance = EventExtractionChain()
    
    return _event_extraction_chain_instance


def extract_events_from_text(text: str) -> List[EventExtractionResponse]:
    """
    便捷函数：从文本中提取事件信息
    
    Args:
        text: 待分析的文本
        
    Returns:
        List[EventExtractionResponse]: 提取到的事件列表
    """
    chain = get_event_extraction_chain()
    return chain.extract_events(text)


async def aextract_events_from_text(text: str) -> List[EventExtractionResponse]:
    """
    便捷函数：异步从文本中提取事件信息
    
    Args:
        text: 待分析的文本
        
    Returns:
        List[EventExtractionResponse]: 提取到的事件列表
    """
    chain = get_event_extraction_chain()
    return await chain.aextract_events(text)
